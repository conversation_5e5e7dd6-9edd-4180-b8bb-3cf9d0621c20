from flask import Flask, render_template, request

app = Flask(__name__)

def calculate_salary(gross_salary):
    """
    Calculates income tax, pension, and net income based on Ethiopian regulations.
    """
    # Income Tax Calculation
    if gross_salary <= 600:
        tax_rate = 0
        deduction = 0
    elif 601 <= gross_salary <= 1650:
        tax_rate = 0.10
        deduction = 60
    elif 1651 <= gross_salary <= 3200:
        tax_rate = 0.15
        deduction = 142.50
    elif 3201 <= gross_salary <= 5250:
        tax_rate = 0.20
        deduction = 302.50
    elif 5251 <= gross_salary <= 7800:
        tax_rate = 0.25
        deduction = 565
    elif 7801 <= gross_salary <= 10900:
        tax_rate = 0.30
        deduction = 955
    else:
        tax_rate = 0.35
        deduction = 1500

    income_tax = (gross_salary * tax_rate) - deduction

    # Pension Calculation
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11

    # Net Income Calculation
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": f"{gross_salary:,.2f} ETB",
        "income_tax": f"{income_tax:,.2f} ETB",
        "employee_pension": f"{employee_pension:,.2f} ETB",
        "employer_pension": f"{employer_pension:,.2f} ETB",
        "net_income": f"{net_income:,.2f} ETB"
    }

@app.route('/', methods=['GET', 'POST'])
def index():
    results = None
    if request.method == 'POST':
        try:
            gross_salary = float(request.form['gross_salary'])
            results = calculate_salary(gross_salary)
        except ValueError:
            results = {"error": "Please enter a valid number for the salary."}
    return render_template('salary.html', results=results)

if __name__ == '__main__':
    # Create a simple HTML template file named 'index.html' in a 'templates' folder.
    # The content of 'index.html' is provided below.
    app.run(debug=True)