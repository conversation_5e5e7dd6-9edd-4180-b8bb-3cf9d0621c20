from flask import Flask, render_template, request

app = Flask(__name__)

def calculate_salary(gross_salary):
    """
    Calculates income tax, pension, and net income using the new formula:
    =IF(G8<=2000,0,IF(G8<=4000,(G8-2000)*15%, IF(G8<=7001,300+(G8-4000)*20%,IF(G8<=10001,900+(G8-7000)*25%,IF(G8<=14000,1650+(G8-10000)*30%,IF(G8>14000,2850+(G8-14000)*35%))))))
    """
    # Income Tax Calculation using the new progressive formula
    if gross_salary <= 2000:
        income_tax = 0
    elif gross_salary <= 4000:
        income_tax = (gross_salary - 2000) * 0.15
    elif gross_salary <= 7000:
        income_tax = 300 + (gross_salary - 4000) * 0.20
    elif gross_salary <= 10000:
        income_tax = 900 + (gross_salary - 7000) * 0.25
    elif gross_salary <= 14000:
        income_tax = 1650 + (gross_salary - 10000) * 0.30
    else:  # gross_salary > 14000
        income_tax = 2850 + (gross_salary - 14000) * 0.35

    # Ensure tax is not negative
    income_tax = max(0, income_tax)

    # Pension Calculation
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11

    # Net Income Calculation
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": f"{gross_salary:,.2f} ETB",
        "income_tax": f"{income_tax:,.2f} ETB",
        "employee_pension": f"{employee_pension:,.2f} ETB",
        "employer_pension": f"{employer_pension:,.2f} ETB",
        "net_income": f"{net_income:,.2f} ETB"
    }

@app.route('/', methods=['GET', 'POST'])
def index():
    results = None
    if request.method == 'POST':
        try:
            gross_salary = float(request.form['gross_salary'])
            results = calculate_salary(gross_salary)
        except ValueError:
            results = {"error": "Please enter a valid number for the salary."}
    return render_template('salary.html', results=results)

if __name__ == '__main__':
    # Create a simple HTML template file named 'index.html' in a 'templates' folder.
    # The content of 'index.html' is provided below.
    app.run(debug=True)