#!/usr/bin/env python3
"""
Test script to verify the Flask app functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import calculate_salary

def test_flask_function():
    """
    Test the calculate_salary function from the Flask app
    """
    print("=" * 60)
    print("TESTING FLASK APP CALCULATE_SALARY FUNCTION")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        500,    # Low salary
        1000,   # Medium salary
        5000,   # Higher salary
        12000   # High salary
    ]
    
    for salary in test_cases:
        result = calculate_salary(salary)
        print(f"\nTesting salary: {salary:,.2f} ETB")
        print(f"Results:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        print("-" * 40)

def test_error_handling():
    """
    Test error handling scenarios
    """
    print("\n" + "=" * 60)
    print("TESTING ERROR HANDLING")
    print("=" * 60)
    
    # Test with negative salary
    try:
        result = calculate_salary(-1000)
        print(f"Negative salary test: {result}")
    except Exception as e:
        print(f"Error with negative salary: {e}")
    
    # Test with zero salary
    try:
        result = calculate_salary(0)
        print(f"Zero salary test: {result}")
    except Exception as e:
        print(f"Error with zero salary: {e}")
    
    # Test with very large salary
    try:
        result = calculate_salary(1000000)
        print(f"Very large salary test: {result}")
    except Exception as e:
        print(f"Error with very large salary: {e}")

if __name__ == "__main__":
    test_flask_function()
    test_error_handling()
