<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethiopian Tax Calculator - Complete System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1em;
        }
        
        .container { 
            max-width: 1400px; 
            margin: auto; 
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 2em;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 0.5em;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 2em;
        }
        
        .tabs {
            display: flex;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 2em;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 1em;
            text-align: center;
            background-color: #e9ecef;
            cursor: pointer;
            border: none;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        
        .tab:hover {
            background-color: #0056b3;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .message {
            padding: 1em;
            border-radius: 8px;
            margin-bottom: 1em;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .calculator-section {
            background: #f8f9fa;
            padding: 2em;
            border-radius: 10px;
            margin-bottom: 2em;
        }
        
        .input-group {
            margin-bottom: 1.5em;
        }
        
        .input-group label {
            display: block;
            font-weight: bold;
            margin-bottom: 0.5em;
            color: #2c3e50;
        }
        
        .input-group input {
            width: 100%;
            padding: 1em;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1.1em;
            transition: border-color 0.3s ease;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1em 2em;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        
        .btn-update {
            background: linear-gradient(135deg, #17a2b8 0%, #**********%);
        }
        
        .btn-update:hover {
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        
        .results {
            margin-top: 2em;
            padding: 2em;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5em;
            margin-top: 1.5em;
        }
        
        .system-box {
            padding: 1.5em;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .old-system {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
        }
        
        .new-system {
            background: linear-gradient(135deg, #d1ecf1 0%, #74b9ff 100%);
            border: 2px solid #17a2b8;
        }
        
        .difference {
            background: linear-gradient(135deg, #d4edda 0%, #00b894 100%);
            border: 2px solid #28a745;
        }
        
        .tables-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2em;
            margin-bottom: 2em;
        }
        
        .table-section {
            background: white;
            padding: 1.5em;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .table-section h3 {
            color: #2c3e50;
            margin-bottom: 1em;
            text-align: center;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1em;
        }
        
        th, td {
            padding: 0.8em;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 2em;
            border-radius: 10px;
            margin-top: 2em;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 1em;
            margin-bottom: 1em;
            align-items: center;
        }
        
        .form-row label {
            font-weight: bold;
            text-align: center;
            color: #2c3e50;
        }
        
        .form-row input {
            padding: 0.8em;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9em;
        }
        
        .summary-box {
            background: linear-gradient(135deg, #e8f4f8 0%, #d1ecf1 100%);
            padding: 1.5em;
            border-radius: 10px;
            margin-top: 1.5em;
            border-left: 5px solid #17a2b8;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .tables-container {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 Ethiopian Tax Calculator</h1>
            <p>Complete Tax System Comparison & Management</p>
        </div>

        <div class="main-content">
            {% if message %}
                <div class="message {{ 'success' if '✅' in message else 'error' }}">
                    {{ message }}
                </div>
            {% endif %}

            <!-- Tab Navigation -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('calculator')">💰 Calculator</button>
                <button class="tab" onclick="showTab('tables')">📊 Tax Tables</button>
                <button class="tab" onclick="showTab('editor')">✏️ Edit Tax Slabs</button>
            </div>

            <!-- Calculator Tab -->
            <div id="calculator" class="tab-content active">
                <div class="calculator-section">
                    <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1em;">Salary Calculator</h2>
                    <form method="post">
                        <div class="input-group">
                            <label for="gross_salary">Enter Gross Monthly Salary (ETB):</label>
                            <input type="number" id="gross_salary" name="gross_salary" placeholder="e.g., 5000" step="0.01" required>
                        </div>
                        <button type="submit" class="btn">🔍 Calculate & Compare</button>
                    </form>
                </div>

                {% if results %}
                    <div class="results">
                        {% if results.error %}
                            <div class="message error">{{ results.error }}</div>
                        {% else %}
                            <h2 style="text-align: center; color: #2c3e50;">Comparison Results for {{ results.gross_salary }}</h2>

                            <div class="comparison-grid">
                                <div class="system-box old-system">
                                    <h3>📊 OLD System</h3>
                                    <p><strong>Income Tax:</strong><br>{{ results.old.income_tax }}</p>
                                    <p><strong>Employee Pension:</strong><br>{{ results.old.employee_pension }}</p>
                                    <p><strong>Net Income:</strong><br><strong>{{ results.old.net_income }}</strong></p>
                                </div>

                                <div class="system-box new-system">
                                    <h3>🆕 NEW System</h3>
                                    <p><strong>Income Tax:</strong><br>{{ results.new.income_tax }}</p>
                                    <p><strong>Employee Pension:</strong><br>{{ results.new.employee_pension }}</p>
                                    <p><strong>Net Income:</strong><br><strong>{{ results.new.net_income }}</strong></p>
                                </div>

                                <div class="system-box difference">
                                    <h3>📈 Difference</h3>
                                    <p><strong>Tax Change:</strong><br>{{ results.differences.tax_difference }}<br><small>({{ results.differences.tax_change_percent }})</small></p>
                                    <p><strong>Net Change:</strong><br><strong>{{ results.differences.net_difference }}</strong><br><small>({{ results.differences.net_change_percent }})</small></p>
                                </div>
                            </div>

                            <div class="summary-box">
                                <h4>💡 Summary:</h4>
                                <p><strong>Gross Salary:</strong> {{ results.gross_salary }}</p>
                                <p><strong>Employer Pension (11%):</strong> {{ results.old.employer_pension }} (same in both systems)</p>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <!-- Tax Tables Tab -->
            <div id="tables" class="tab-content">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1em;">Tax Tables Comparison</h2>

                <div class="tables-container">
                    <div class="table-section">
                        <h3 style="color: #856404;">📊 OLD Tax System</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Salary Range (ETB)</th>
                                    <th>Tax Rate</th>
                                    <th>Deduction (ETB)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for slab in old_slabs %}
                                <tr>
                                    <td>
                                        {% if slab.max > 999999 %}
                                            {{ "{:,.0f}".format(slab.min) }}+
                                        {% else %}
                                            {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }}
                                        {% endif %}
                                    </td>
                                    <td>{{ "{:.0f}".format(slab.rate * 100) }}%</td>
                                    <td>{{ "{:,.2f}".format(slab.deduction) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <div class="table-section">
                        <h3 style="color: #0c5460;">🆕 NEW Tax System</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Salary Range (ETB)</th>
                                    <th>Tax Rate</th>
                                    <th>Deduction (ETB)</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for slab in new_slabs %}
                                <tr>
                                    <td>
                                        {% if slab.max > 999999 %}
                                            {{ "{:,.0f}".format(slab.min) }}+
                                        {% else %}
                                            {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }}
                                        {% endif %}
                                    </td>
                                    <td>{{ "{:.0f}".format(slab.rate * 100) }}%</td>
                                    <td>{{ "{:,.2f}".format(slab.deduction) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Editor Tab -->
            <div id="editor" class="tab-content">
                <h2 style="text-align: center; color: #2c3e50; margin-bottom: 1em;">Edit NEW Tax Slabs</h2>

                <div class="form-section">
                    <p style="text-align: center; margin-bottom: 2em; color: #6c757d;">
                        Update the tax slabs below and click "Update Tax Slabs" to apply changes. Use ∞ for the highest bracket's maximum.
                    </p>

                    <form method="post">
                        <div class="form-row">
                            <label>Min Salary (ETB)</label>
                            <label>Max Salary (ETB)</label>
                            <label>Tax Rate (%)</label>
                            <label>Deduction (ETB)</label>
                        </div>

                        {% for i in range(7) %}
                        <div class="form-row">
                            <input type="number" name="min_{{ i }}"
                                   value="{{ saved_form_data.get('min_' + i|string, new_slabs[i].min if new_slabs[i].min < 999999 else '') }}"
                                   step="0.01" required>
                            <input type="text" name="max_{{ i }}"
                                   value="{{ saved_form_data.get('max_' + i|string, '∞' if new_slabs[i].max > 999999 else new_slabs[i].max) }}"
                                   placeholder="Enter max or ∞">
                            <input type="number" name="rate_{{ i }}"
                                   value="{{ saved_form_data.get('rate_' + i|string, (new_slabs[i].rate * 100)|round(1)) }}"
                                   step="0.1" min="0" max="100" required>
                            <input type="number" name="deduction_{{ i }}"
                                   value="{{ saved_form_data.get('deduction_' + i|string, new_slabs[i].deduction) }}"
                                   step="0.01" min="0" required>
                        </div>
                        {% endfor %}

                        <button type="submit" class="btn btn-update">🔄 Update Tax Slabs</button>
                    </form>
                </div>

                <div class="summary-box">
                    <h4>💡 Instructions:</h4>
                    <ul>
                        <li><strong>Min/Max Salary:</strong> Define the salary range for each tax bracket</li>
                        <li><strong>Tax Rate:</strong> Enter percentage (0-100)</li>
                        <li><strong>Deduction:</strong> Fixed amount subtracted from calculated tax</li>
                        <li><strong>Formula:</strong> Income Tax = (Gross Salary × Tax Rate) - Deduction</li>
                        <li><strong>Highest Bracket:</strong> Use ∞ (infinity symbol) for unlimited upper range</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Auto-scroll to results if they exist
        {% if results %}
        document.addEventListener('DOMContentLoaded', function() {
            const resultsElement = document.querySelector('.results');
            if (resultsElement) {
                resultsElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
        {% endif %}

        // Auto-switch to calculator tab if there are results
        {% if results %}
        document.addEventListener('DOMContentLoaded', function() {
            showTab('calculator');
        });
        {% endif %}

        // Auto-switch to editor tab if there's a message about tax slabs
        {% if message and '✅' in message %}
        document.addEventListener('DOMContentLoaded', function() {
            showTab('tables');
        });
        {% endif %}

        // Form persistence with local storage
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('#editor form');
            if (form) {
                // Save form data on input change
                form.addEventListener('input', function(e) {
                    if (e.target.name && e.target.name.match(/^(min_|max_|rate_|deduction_)/)) {
                        localStorage.setItem('tax_form_' + e.target.name, e.target.value);
                    }
                });

                // Load form data from local storage if no server data
                {% if not saved_form_data %}
                const inputs = form.querySelectorAll('input[name^="min_"], input[name^="max_"], input[name^="rate_"], input[name^="deduction_"]');
                inputs.forEach(input => {
                    const savedValue = localStorage.getItem('tax_form_' + input.name);
                    if (savedValue !== null && input.value === '') {
                        input.value = savedValue;
                    }
                });
                {% endif %}

                // Clear local storage when form is successfully submitted
                {% if message and '✅' in message %}
                const inputs = form.querySelectorAll('input[name^="min_"], input[name^="max_"], input[name^="rate_"], input[name^="deduction_"]');
                inputs.forEach(input => {
                    localStorage.removeItem('tax_form_' + input.name);
                });
                {% endif %}
            }
        });

        // Add visual feedback for unsaved changes
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('#editor form');
            const submitBtn = form ? form.querySelector('button[type="submit"]') : null;
            let hasUnsavedChanges = false;

            if (form && submitBtn) {
                form.addEventListener('input', function() {
                    hasUnsavedChanges = true;
                    submitBtn.innerHTML = '🔄 Update Tax Slabs (Unsaved Changes)';
                    submitBtn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
                });

                form.addEventListener('submit', function() {
                    hasUnsavedChanges = false;
                    submitBtn.innerHTML = '🔄 Update Tax Slabs';
                    submitBtn.style.background = 'linear-gradient(135deg, #17a2b8 0%, #**********%)';
                });
            }
        });
    </script>
</body>
</html>
