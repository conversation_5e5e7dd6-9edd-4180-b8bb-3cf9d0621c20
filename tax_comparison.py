#!/usr/bin/env python3
"""
Ethiopian Tax Comparison System - Old vs New Tax Slabs
"""

def calculate_old_tax(gross_salary):
    """
    Calculate tax using OLD Ethiopian tax slabs
    TODO: Update with actual old tax brackets when provided
    """
    # PLACEHOLDER - Current implementation (will be updated with old rates)
    if gross_salary <= 600:
        tax_rate = 0
        deduction = 0
    elif 601 <= gross_salary <= 1650:
        tax_rate = 0.10
        deduction = 60
    elif 1651 <= gross_salary <= 3200:
        tax_rate = 0.15
        deduction = 142.50
    elif 3201 <= gross_salary <= 5250:
        tax_rate = 0.20
        deduction = 302.50
    elif 5251 <= gross_salary <= 7800:
        tax_rate = 0.25
        deduction = 565
    elif 7801 <= gross_salary <= 10900:
        tax_rate = 0.30
        deduction = 955
    else:
        tax_rate = 0.35
        deduction = 1500

    income_tax = (gross_salary * tax_rate) - deduction
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income,
        "tax_rate": tax_rate,
        "deduction": deduction
    }

def calculate_new_tax(gross_salary):
    """
    Calculate tax using NEW Ethiopian tax slabs
    TODO: Update with actual new tax brackets when provided
    """
    # PLACEHOLDER - Current implementation (will be updated with new rates)
    if gross_salary <= 600:
        tax_rate = 0
        deduction = 0
    elif 601 <= gross_salary <= 1650:
        tax_rate = 0.10
        deduction = 60
    elif 1651 <= gross_salary <= 3200:
        tax_rate = 0.15
        deduction = 142.50
    elif 3201 <= gross_salary <= 5250:
        tax_rate = 0.20
        deduction = 302.50
    elif 5251 <= gross_salary <= 7800:
        tax_rate = 0.25
        deduction = 565
    elif 7801 <= gross_salary <= 10900:
        tax_rate = 0.30
        deduction = 955
    else:
        tax_rate = 0.35
        deduction = 1500

    income_tax = (gross_salary * tax_rate) - deduction
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income,
        "tax_rate": tax_rate,
        "deduction": deduction
    }

def display_tax_tables():
    """
    Display both old and new tax tables side by side
    """
    print("=" * 100)
    print("ETHIOPIAN TAX SLABS COMPARISON")
    print("=" * 100)
    
    print("\n" + "=" * 50)
    print("OLD TAX SLABS")
    print("=" * 50)
    print("| Salary Range (ETB)    | Tax Rate | Deduction (ETB) |")
    print("|----------------------|----------|-----------------|")
    print("| <= 600               | 0%       | 0               |")
    print("| 601 - 1,650          | 10%      | 60              |")
    print("| 1,651 - 3,200        | 15%      | 142.50          |")
    print("| 3,201 - 5,250        | 20%      | 302.50          |")
    print("| 5,251 - 7,800        | 25%      | 565             |")
    print("| 7,801 - 10,900       | 30%      | 955             |")
    print("| > 10,900             | 35%      | 1,500           |")
    
    print("\n" + "=" * 50)
    print("NEW TAX SLABS")
    print("=" * 50)
    print("| Salary Range (ETB)    | Tax Rate | Deduction (ETB) |")
    print("|----------------------|----------|-----------------|")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")
    print("| [TO BE UPDATED]      | [TBU]    | [TBU]           |")

def compare_calculations(test_salaries):
    """
    Compare calculations between old and new tax systems
    """
    print("\n" + "=" * 120)
    print("SALARY CALCULATION COMPARISON")
    print("=" * 120)
    
    print(f"{'Salary (ETB)':<12} {'OLD Tax':<10} {'OLD Net':<12} {'NEW Tax':<10} {'NEW Net':<12} {'Tax Diff':<10} {'Net Diff':<10}")
    print("-" * 120)
    
    for salary in test_salaries:
        old_result = calculate_old_tax(salary)
        new_result = calculate_new_tax(salary)
        
        tax_diff = new_result['income_tax'] - old_result['income_tax']
        net_diff = new_result['net_income'] - old_result['net_income']
        
        print(f"{salary:<12,.0f} {old_result['income_tax']:<10,.2f} {old_result['net_income']:<12,.2f} "
              f"{new_result['income_tax']:<10,.2f} {new_result['net_income']:<12,.2f} "
              f"{tax_diff:<10,.2f} {net_diff:<10,.2f}")

def detailed_comparison(salary):
    """
    Show detailed comparison for a specific salary
    """
    old_result = calculate_old_tax(salary)
    new_result = calculate_new_tax(salary)
    
    print(f"\n" + "=" * 80)
    print(f"DETAILED COMPARISON FOR {salary:,.2f} ETB")
    print("=" * 80)
    
    print(f"{'Component':<25} {'OLD System':<15} {'NEW System':<15} {'Difference':<15}")
    print("-" * 80)
    print(f"{'Gross Salary':<25} {old_result['gross_salary']:<15,.2f} {new_result['gross_salary']:<15,.2f} {0:<15,.2f}")
    print(f"{'Tax Rate':<25} {old_result['tax_rate']*100:<15.1f}% {new_result['tax_rate']*100:<15.1f}% {(new_result['tax_rate']-old_result['tax_rate'])*100:<15.1f}%")
    print(f"{'Tax Deduction':<25} {old_result['deduction']:<15,.2f} {new_result['deduction']:<15,.2f} {new_result['deduction']-old_result['deduction']:<15,.2f}")
    print(f"{'Income Tax':<25} {old_result['income_tax']:<15,.2f} {new_result['income_tax']:<15,.2f} {new_result['income_tax']-old_result['income_tax']:<15,.2f}")
    print(f"{'Employee Pension (7%)':<25} {old_result['employee_pension']:<15,.2f} {new_result['employee_pension']:<15,.2f} {0:<15,.2f}")
    print(f"{'Net Income':<25} {old_result['net_income']:<15,.2f} {new_result['net_income']:<15,.2f} {new_result['net_income']-old_result['net_income']:<15,.2f}")

if __name__ == "__main__":
    # Display tax tables
    display_tax_tables()
    
    # Test salaries for comparison
    test_salaries = [500, 1000, 2000, 3000, 4000, 5000, 6000, 8000, 10000, 12000, 15000]
    
    # Compare calculations
    compare_calculations(test_salaries)
    
    # Detailed comparison for specific salary
    detailed_comparison(5000)
    
    print(f"\n" + "=" * 80)
    print("NOTE: Please provide the new tax slabs to update the comparison")
    print("=" * 80)
