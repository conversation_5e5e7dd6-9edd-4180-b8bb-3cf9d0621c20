#!/usr/bin/env python3
"""
Test script to verify form persistence functionality
"""

import requests
import time

def test_form_persistence():
    """Test that form data is remembered between sessions"""
    base_url = "http://127.0.0.1:5002"
    
    print("=" * 60)
    print("TESTING FORM PERSISTENCE")
    print("=" * 60)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Test data for new tax slabs
    test_data = {
        'min_0': '0',
        'max_0': '800',
        'rate_0': '0',
        'deduction_0': '0',
        'min_1': '801',
        'max_1': '2000',
        'rate_1': '12',
        'deduction_1': '80',
        'min_2': '2001',
        'max_2': '3500',
        'rate_2': '18',
        'deduction_2': '200',
        'min_3': '3501',
        'max_3': '5500',
        'rate_3': '25',
        'deduction_3': '400',
        'min_4': '5501',
        'max_4': '8000',
        'rate_4': '30',
        'deduction_4': '700',
        'min_5': '8001',
        'max_5': '12000',
        'rate_5': '35',
        'deduction_5': '1100',
        'min_6': '12001',
        'max_6': '∞',
        'rate_6': '40',
        'deduction_6': '1700'
    }
    
    print("Step 1: Submitting test tax slab data...")
    response = session.post(base_url, data=test_data)
    
    if response.status_code == 200:
        print("✅ Tax slab data submitted successfully")
        
        # Check if the success message is in the response
        if "✅" in response.text and "updated" in response.text:
            print("✅ Success message found in response")
        else:
            print("⚠️ Success message not found in response")
    else:
        print(f"❌ Failed to submit data. Status code: {response.status_code}")
        return
    
    print("\nStep 2: Making a new request to check if data is remembered...")
    time.sleep(1)  # Brief pause
    
    response2 = session.get(base_url)
    
    if response2.status_code == 200:
        print("✅ Second request successful")
        
        # Check if our test values are present in the form
        test_values_found = 0
        for key, value in test_data.items():
            if f'value="{value}"' in response2.text or f"value={value}" in response2.text:
                test_values_found += 1
        
        print(f"📊 Found {test_values_found}/{len(test_data)} test values in the form")
        
        if test_values_found >= len(test_data) * 0.8:  # At least 80% of values found
            print("✅ Form persistence is working correctly!")
        else:
            print("⚠️ Some form values may not be persisting correctly")
            
        # Check specific key values
        key_checks = [
            ('max_0', '800'),
            ('rate_1', '12'),
            ('max_6', '∞')
        ]
        
        print("\nKey value checks:")
        for field, expected in key_checks:
            if f'value="{expected}"' in response2.text or f"value={expected}" in response2.text:
                print(f"✅ {field} = {expected}")
            else:
                print(f"❌ {field} ≠ {expected}")
    else:
        print(f"❌ Failed to get page. Status code: {response2.status_code}")

def test_calculator_with_new_slabs():
    """Test calculator with the new tax slabs"""
    print("\n" + "=" * 60)
    print("TESTING CALCULATOR WITH NEW TAX SLABS")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5002"
    session = requests.Session()
    
    # Test salary calculation
    calc_data = {'gross_salary': '5000'}
    
    print("Testing salary calculation with 5000 ETB...")
    response = session.post(base_url, data=calc_data)
    
    if response.status_code == 200:
        print("✅ Calculator request successful")
        
        # Look for comparison results
        if "Comparison Results" in response.text:
            print("✅ Comparison results found")
            
            # Check for different tax amounts (indicating new slabs are being used)
            if "OLD System" in response.text and "NEW System" in response.text:
                print("✅ Both OLD and NEW systems displayed")
            else:
                print("⚠️ System comparison not found")
        else:
            print("⚠️ Comparison results not found")
    else:
        print(f"❌ Calculator test failed. Status code: {response.status_code}")

if __name__ == "__main__":
    try:
        test_form_persistence()
        test_calculator_with_new_slabs()
        
        print("\n" + "=" * 60)
        print("PERSISTENCE TESTING COMPLETE")
        print("=" * 60)
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the application. Make sure it's running on http://127.0.0.1:5002")
    except Exception as e:
        print(f"❌ Error during testing: {e}")
