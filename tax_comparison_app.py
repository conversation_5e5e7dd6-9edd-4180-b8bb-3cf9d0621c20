#!/usr/bin/env python3
"""
Flask app for Ethiopian Tax Comparison - Old vs New
"""

from flask import Flask, render_template, request
import json

app = Flask(__name__)

# OLD TAX SLABS (Current system from your app.py)
OLD_TAX_SLABS = [
    {"min": 0, "max": 600, "rate": 0.00, "deduction": 0},
    {"min": 601, "max": 1650, "rate": 0.10, "deduction": 60},
    {"min": 1651, "max": 3200, "rate": 0.15, "deduction": 142.50},
    {"min": 3201, "max": 5250, "rate": 0.20, "deduction": 302.50},
    {"min": 5251, "max": 7800, "rate": 0.25, "deduction": 565},
    {"min": 7801, "max": 10900, "rate": 0.30, "deduction": 955},
    {"min": 10901, "max": float('inf'), "rate": 0.35, "deduction": 1500}
]

# NEW TAX SLABS (To be updated when you provide the data)
NEW_TAX_SLABS = [
    {"min": 0, "max": 600, "rate": 0.00, "deduction": 0},
    {"min": 601, "max": 1650, "rate": 0.10, "deduction": 60},
    {"min": 1651, "max": 3200, "rate": 0.15, "deduction": 142.50},
    {"min": 3201, "max": 5250, "rate": 0.20, "deduction": 302.50},
    {"min": 5251, "max": 7800, "rate": 0.25, "deduction": 565},
    {"min": 7801, "max": 10900, "rate": 0.30, "deduction": 955},
    {"min": 10901, "max": float('inf'), "rate": 0.35, "deduction": 1500}
]

def calculate_tax_with_slabs(gross_salary, tax_slabs):
    """Calculate tax using provided tax slabs"""
    for slab in tax_slabs:
        if slab["min"] <= gross_salary <= slab["max"]:
            income_tax = (gross_salary * slab["rate"]) - slab["deduction"]
            break
    else:
        # If no slab found, use the highest slab
        income_tax = (gross_salary * tax_slabs[-1]["rate"]) - tax_slabs[-1]["deduction"]
    
    # Ensure tax is not negative
    income_tax = max(0, income_tax)
    
    # Pension calculations (assuming same for both systems)
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11
    net_income = gross_salary - income_tax - employee_pension
    
    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income
    }

def format_currency(amount):
    """Format amount as Ethiopian Birr"""
    return f"{amount:,.2f} ETB"

@app.route('/', methods=['GET', 'POST'])
def index():
    results = None
    if request.method == 'POST':
        try:
            gross_salary = float(request.form['gross_salary'])
            
            # Calculate with both old and new systems
            old_result = calculate_tax_with_slabs(gross_salary, OLD_TAX_SLABS)
            new_result = calculate_tax_with_slabs(gross_salary, NEW_TAX_SLABS)
            
            # Calculate differences
            tax_difference = new_result['income_tax'] - old_result['income_tax']
            net_difference = new_result['net_income'] - old_result['net_income']
            
            results = {
                "gross_salary": format_currency(gross_salary),
                "old": {
                    "income_tax": format_currency(old_result['income_tax']),
                    "employee_pension": format_currency(old_result['employee_pension']),
                    "employer_pension": format_currency(old_result['employer_pension']),
                    "net_income": format_currency(old_result['net_income'])
                },
                "new": {
                    "income_tax": format_currency(new_result['income_tax']),
                    "employee_pension": format_currency(new_result['employee_pension']),
                    "employer_pension": format_currency(new_result['employer_pension']),
                    "net_income": format_currency(new_result['net_income'])
                },
                "differences": {
                    "tax_difference": format_currency(tax_difference),
                    "net_difference": format_currency(net_difference),
                    "tax_change_percent": f"{(tax_difference/old_result['income_tax']*100) if old_result['income_tax'] > 0 else 0:.1f}%",
                    "net_change_percent": f"{(net_difference/old_result['net_income']*100) if old_result['net_income'] > 0 else 0:.1f}%"
                }
            }
            
        except ValueError:
            results = {"error": "Please enter a valid number for the salary."}
    
    return render_template('tax_comparison.html', results=results, old_slabs=OLD_TAX_SLABS, new_slabs=NEW_TAX_SLABS)

@app.route('/tables', methods=['GET', 'POST'])
def tax_tables():
    """Display tax tables comparison and allow editing"""
    global NEW_TAX_SLABS
    message = None

    if request.method == 'POST':
        try:
            # Update NEW_TAX_SLABS with form data
            updated_slabs = []
            for i in range(7):  # Assuming 7 tax slabs
                min_val = float(request.form.get(f'min_{i}', 0))
                max_val = request.form.get(f'max_{i}', '')
                rate = float(request.form.get(f'rate_{i}', 0)) / 100  # Convert percentage to decimal
                deduction = float(request.form.get(f'deduction_{i}', 0))

                # Handle the last slab (infinity)
                if max_val == '' or max_val.lower() == 'inf' or max_val == '∞':
                    max_val = float('inf')
                else:
                    max_val = float(max_val)

                updated_slabs.append({
                    "min": min_val,
                    "max": max_val,
                    "rate": rate,
                    "deduction": deduction
                })

            NEW_TAX_SLABS = updated_slabs
            message = "✅ New tax slabs updated successfully!"

        except ValueError as e:
            message = f"❌ Error updating tax slabs: Please check your input values."
        except Exception as e:
            message = f"❌ Error: {str(e)}"

    return render_template('tax_tables.html', old_slabs=OLD_TAX_SLABS, new_slabs=NEW_TAX_SLABS, message=message)

if __name__ == '__main__':
    app.run(debug=True, port=5001)
