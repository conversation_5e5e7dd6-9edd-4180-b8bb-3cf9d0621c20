<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethiopian Payroll Calculator</title>
    <style>
        body { font-family: sans-serif; margin: 2em; }
        .container { max-width: 600px; margin: auto; padding: 1em; border: 1px solid #ccc; border-radius: 5px; }
        .results { margin-top: 1em; padding: 1em; border: 1px solid #ddd; background-color: #f9f9f9; }
        .results p { margin: 0.5em 0; }
        input[type="text"], input[type="submit"] { width: 100%; padding: 0.5em; margin-bottom: 1em; }
        input[type="submit"] { background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Ethiopian Salary Calculator</h1>
        <form method="post">
            <label for="gross_salary">Enter Gross Monthly Salary (ETB):</label>
            <input type="text" id="gross_salary" name="gross_salary" required>
            <input type="submit" value="Calculate">
        </form>

        {% if results %}
            <div class="results">
                {% if results.error %}
                    <p class="error">{{ results.error }}</p>
                {% else %}
                    <h2>Calculation Results:</h2>
                    <p><strong>Gross Salary:</strong> {{ results.gross_salary }}</p>
                    <p><strong>Income Tax:</strong> {{ results.income_tax }}</p>
                    <p><strong>Employee Pension (7%):</strong> {{ results.employee_pension }}</p>
                    <p><strong>Employer Pension (11%):</strong> {{ results.employer_pension }}</p>
                    <hr>
                    <p><strong>Net Income (Take-Home Pay):</strong> {{ results.net_income }}</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</body>
</html>