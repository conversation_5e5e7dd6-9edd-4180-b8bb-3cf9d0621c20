#!/usr/bin/env python3
"""
Test script to verify the new tax formula implementation
"""

def calculate_tax_new_formula(gross_salary):
    """
    Calculate tax using the new formula:
    =IF(G8<=2000,0,IF(G8<=4000,(G8-2000)*15%, IF(G8<=7001,300+(G8-4000)*20%,IF(G8<=10001,900+(G8-7000)*25%,IF(G8<=14000,1650+(G8-10000)*30%,IF(G8>14000,2850+(G8-14000)*35%))))))
    """
    if gross_salary <= 2000:
        income_tax = 0
    elif gross_salary <= 4000:
        income_tax = (gross_salary - 2000) * 0.15
    elif gross_salary <= 7000:
        income_tax = 300 + (gross_salary - 4000) * 0.20
    elif gross_salary <= 10000:
        income_tax = 900 + (gross_salary - 7000) * 0.25
    elif gross_salary <= 14000:
        income_tax = 1650 + (gross_salary - 10000) * 0.30
    else:  # gross_salary > 14000
        income_tax = 2850 + (gross_salary - 14000) * 0.35
    
    return max(0, income_tax)

def test_new_formula():
    """Test the new tax formula with various salary amounts"""
    print("=" * 80)
    print("NEW TAX FORMULA TESTING")
    print("=" * 80)
    print("Formula: IF(salary<=2000,0,IF(salary<=4000,(salary-2000)*15%,")
    print("         IF(salary<=7000,300+(salary-4000)*20%,")
    print("         IF(salary<=10000,900+(salary-7000)*25%,")
    print("         IF(salary<=14000,1650+(salary-10000)*30%,")
    print("         IF(salary>14000,2850+(salary-14000)*35%)))))")
    print("=" * 80)
    
    # Test cases covering all brackets and boundaries
    test_cases = [
        # Bracket 1: 0-2000 (0% tax)
        1000, 1500, 2000,
        
        # Bracket 2: 2001-4000 (15% on amount above 2000)
        2001, 3000, 4000,
        
        # Bracket 3: 4001-7000 (300 + 20% on amount above 4000)
        4001, 5000, 6000, 7000,
        
        # Bracket 4: 7001-10000 (900 + 25% on amount above 7000)
        7001, 8000, 9000, 10000,
        
        # Bracket 5: 10001-14000 (1650 + 30% on amount above 10000)
        10001, 12000, 14000,
        
        # Bracket 6: Above 14000 (2850 + 35% on amount above 14000)
        14001, 16000, 20000, 25000
    ]
    
    print(f"{'Salary (ETB)':<12} {'Tax Bracket':<15} {'Income Tax (ETB)':<18} {'Effective Rate':<15}")
    print("-" * 80)
    
    for salary in test_cases:
        tax = calculate_tax_new_formula(salary)
        effective_rate = (tax / salary * 100) if salary > 0 else 0
        
        # Determine bracket
        if salary <= 2000:
            bracket = "0-2000 (0%)"
        elif salary <= 4000:
            bracket = "2001-4000 (15%)"
        elif salary <= 7000:
            bracket = "4001-7000 (20%)"
        elif salary <= 10000:
            bracket = "7001-10000 (25%)"
        elif salary <= 14000:
            bracket = "10001-14000 (30%)"
        else:
            bracket = "14001+ (35%)"
        
        print(f"{salary:<12,.0f} {bracket:<15} {tax:<18,.2f} {effective_rate:<15.2f}%")

def verify_manual_calculations():
    """Verify calculations manually for key test cases"""
    print("\n" + "=" * 80)
    print("MANUAL VERIFICATION")
    print("=" * 80)
    
    test_cases = [
        (3000, "Bracket 2: (3000-2000)*15% = 1000*15% = 150"),
        (5000, "Bracket 3: 300 + (5000-4000)*20% = 300 + 1000*20% = 300 + 200 = 500"),
        (8000, "Bracket 4: 900 + (8000-7000)*25% = 900 + 1000*25% = 900 + 250 = 1150"),
        (12000, "Bracket 5: 1650 + (12000-10000)*30% = 1650 + 2000*30% = 1650 + 600 = 2250"),
        (16000, "Bracket 6: 2850 + (16000-14000)*35% = 2850 + 2000*35% = 2850 + 700 = 3550")
    ]
    
    for salary, explanation in test_cases:
        calculated_tax = calculate_tax_new_formula(salary)
        print(f"\nSalary: {salary:,} ETB")
        print(f"Calculation: {explanation}")
        print(f"Result: {calculated_tax:,.2f} ETB")

def compare_with_old_system():
    """Compare new formula with old system"""
    print("\n" + "=" * 80)
    print("COMPARISON WITH OLD SYSTEM")
    print("=" * 80)
    
    def calculate_old_tax(gross_salary):
        """Old tax calculation for comparison"""
        if gross_salary <= 600:
            tax_rate = 0
            deduction = 0
        elif 601 <= gross_salary <= 1650:
            tax_rate = 0.10
            deduction = 60
        elif 1651 <= gross_salary <= 3200:
            tax_rate = 0.15
            deduction = 142.50
        elif 3201 <= gross_salary <= 5250:
            tax_rate = 0.20
            deduction = 302.50
        elif 5251 <= gross_salary <= 7800:
            tax_rate = 0.25
            deduction = 565
        elif 7801 <= gross_salary <= 10900:
            tax_rate = 0.30
            deduction = 955
        else:
            tax_rate = 0.35
            deduction = 1500
        
        return max(0, (gross_salary * tax_rate) - deduction)
    
    comparison_salaries = [2000, 4000, 6000, 8000, 10000, 12000, 15000, 20000]
    
    print(f"{'Salary':<8} {'OLD Tax':<10} {'NEW Tax':<10} {'Difference':<12} {'Change %':<10}")
    print("-" * 60)
    
    for salary in comparison_salaries:
        old_tax = calculate_old_tax(salary)
        new_tax = calculate_tax_new_formula(salary)
        difference = new_tax - old_tax
        change_percent = (difference / old_tax * 100) if old_tax > 0 else 0
        
        print(f"{salary:<8,.0f} {old_tax:<10,.2f} {new_tax:<10,.2f} {difference:<12,.2f} {change_percent:<10.1f}%")

if __name__ == "__main__":
    test_new_formula()
    verify_manual_calculations()
    compare_with_old_system()
    
    print("\n" + "=" * 80)
    print("NEW FORMULA TESTING COMPLETE")
    print("=" * 80)
