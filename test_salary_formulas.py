#!/usr/bin/env python3
"""
Test script to verify Ethiopian salary calculation formulas
"""

def calculate_salary(gross_salary):
    """
    Calculates income tax, pension, and net income based on Ethiopian regulations.
    """
    # Income Tax Calculation
    if gross_salary <= 600:
        tax_rate = 0
        deduction = 0
    elif 601 <= gross_salary <= 1650:
        tax_rate = 0.10
        deduction = 60
    elif 1651 <= gross_salary <= 3200:
        tax_rate = 0.15
        deduction = 142.50
    elif 3201 <= gross_salary <= 5250:
        tax_rate = 0.20
        deduction = 302.50
    elif 5251 <= gross_salary <= 7800:
        tax_rate = 0.25
        deduction = 565
    elif 7801 <= gross_salary <= 10900:
        tax_rate = 0.30
        deduction = 955
    else:
        tax_rate = 0.35
        deduction = 1500

    income_tax = (gross_salary * tax_rate) - deduction

    # Pension Calculation
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11

    # Net Income Calculation
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income,
        "tax_rate": tax_rate,
        "deduction": deduction
    }

def test_salary_calculations():
    """
    Test the salary calculation function with various test cases
    """
    print("=" * 60)
    print("ETHIOPIAN SALARY CALCULATION FORMULA TESTING")
    print("=" * 60)
    
    # Test cases covering different tax brackets
    test_cases = [
        500,    # Below 600 (0% tax)
        600,    # Exactly 600 (0% tax)
        601,    # Just above 600 (10% tax)
        1000,   # Middle of 10% bracket
        1650,   # Top of 10% bracket
        1651,   # Bottom of 15% bracket
        2500,   # Middle of 15% bracket
        3200,   # Top of 15% bracket
        3201,   # Bottom of 20% bracket
        4000,   # Middle of 20% bracket
        5250,   # Top of 20% bracket
        5251,   # Bottom of 25% bracket
        6500,   # Middle of 25% bracket
        7800,   # Top of 25% bracket
        7801,   # Bottom of 30% bracket
        9000,   # Middle of 30% bracket
        10900,  # Top of 30% bracket
        12000,  # Above 10900 (35% tax)
        15000   # High salary test
    ]
    
    for gross in test_cases:
        result = calculate_salary(gross)
        
        print(f"\nGross Salary: {gross:,.2f} ETB")
        print(f"Tax Rate: {result['tax_rate']*100:.0f}%")
        print(f"Tax Deduction: {result['deduction']:,.2f} ETB")
        print(f"Income Tax: {result['income_tax']:,.2f} ETB")
        print(f"Employee Pension (7%): {result['employee_pension']:,.2f} ETB")
        print(f"Employer Pension (11%): {result['employer_pension']:,.2f} ETB")
        print(f"Net Income: {result['net_income']:,.2f} ETB")
        print("-" * 40)

def verify_tax_bracket_boundaries():
    """
    Verify that tax calculations are correct at bracket boundaries
    """
    print("\n" + "=" * 60)
    print("TAX BRACKET BOUNDARY VERIFICATION")
    print("=" * 60)
    
    # Test boundary conditions
    boundaries = [
        (600, 601),    # 0% to 10%
        (1650, 1651),  # 10% to 15%
        (3200, 3201),  # 15% to 20%
        (5250, 5251),  # 20% to 25%
        (7800, 7801),  # 25% to 30%
        (10900, 10901) # 30% to 35%
    ]
    
    for lower, upper in boundaries:
        result_lower = calculate_salary(lower)
        result_upper = calculate_salary(upper)
        
        print(f"\nBoundary: {lower} -> {upper}")
        print(f"Lower: Tax={result_lower['income_tax']:,.2f}, Net={result_lower['net_income']:,.2f}")
        print(f"Upper: Tax={result_upper['income_tax']:,.2f}, Net={result_upper['net_income']:,.2f}")
        print(f"Tax difference: {result_upper['income_tax'] - result_lower['income_tax']:,.2f}")

def validate_formula_logic():
    """
    Validate the mathematical logic of the formulas
    """
    print("\n" + "=" * 60)
    print("FORMULA LOGIC VALIDATION")
    print("=" * 60)
    
    # Test edge cases and validate logic
    test_salary = 5000
    result = calculate_salary(test_salary)
    
    print(f"Testing with salary: {test_salary:,.2f} ETB")
    print(f"Expected tax rate: 20% (since 3201 <= 5000 <= 5250)")
    print(f"Actual tax rate: {result['tax_rate']*100:.0f}%")
    
    # Manual calculation
    expected_tax = (test_salary * 0.20) - 302.50
    expected_employee_pension = test_salary * 0.07
    expected_net = test_salary - expected_tax - expected_employee_pension
    
    print(f"\nManual calculation:")
    print(f"Income tax: ({test_salary} * 0.20) - 302.50 = {expected_tax:,.2f}")
    print(f"Employee pension: {test_salary} * 0.07 = {expected_employee_pension:,.2f}")
    print(f"Net income: {test_salary} - {expected_tax:,.2f} - {expected_employee_pension:,.2f} = {expected_net:,.2f}")
    
    print(f"\nFormula results:")
    print(f"Income tax: {result['income_tax']:,.2f}")
    print(f"Employee pension: {result['employee_pension']:,.2f}")
    print(f"Net income: {result['net_income']:,.2f}")
    
    # Validation
    tax_match = abs(result['income_tax'] - expected_tax) < 0.01
    pension_match = abs(result['employee_pension'] - expected_employee_pension) < 0.01
    net_match = abs(result['net_income'] - expected_net) < 0.01
    
    print(f"\nValidation:")
    print(f"Tax calculation correct: {tax_match}")
    print(f"Pension calculation correct: {pension_match}")
    print(f"Net income calculation correct: {net_match}")
    print(f"All calculations correct: {tax_match and pension_match and net_match}")

if __name__ == "__main__":
    test_salary_calculations()
    verify_tax_bracket_boundaries()
    validate_formula_logic()
    
    print("\n" + "=" * 60)
    print("TESTING COMPLETE")
    print("=" * 60)
