<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethiopian Tax Tables Comparison</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 2em; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: auto; 
            padding: 2em; 
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 2em;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 2em;
        }
        .nav-links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 1em;
            font-weight: bold;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .tables-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2em;
            margin-top: 2em;
        }
        .table-section {
            padding: 1.5em;
            border-radius: 10px;
        }
        .old-table {
            background-color: #fff8e1;
            border: 2px solid #ffc107;
        }
        .new-table {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1em;
        }
        th, td {
            padding: 0.8em;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: rgba(0,0,0,0.1);
            font-weight: bold;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            color: #856404;
            padding: 1em;
            border-radius: 5px;
            margin-bottom: 2em;
        }
        .placeholder {
            color: #6c757d;
            font-style: italic;
        }
        .highlight {
            background-color: #ffffcc;
        }
        .message {
            padding: 1em;
            border-radius: 5px;
            margin-bottom: 1em;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .editable-form {
            background-color: #f8f9fa;
            padding: 2em;
            border-radius: 10px;
            margin: 2em 0;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 1em;
            margin-bottom: 1em;
            align-items: center;
        }
        .form-row input {
            padding: 0.5em;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .form-row label {
            font-weight: bold;
            text-align: center;
        }
        .submit-btn {
            background-color: #28a745;
            color: white;
            padding: 1em 2em;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin-top: 1em;
        }
        .submit-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 Ethiopian Tax Tables Comparison</h1>
            <p>Side-by-Side View of Old vs New Tax Slabs</p>
        </div>

        <div class="nav-links">
            <a href="/">Calculator</a>
            <a href="/tables">Tax Tables</a>
        </div>

        {% if message %}
            <div class="message {{ 'success' if '✅' in message else 'error' }}">
                {{ message }}
            </div>
        {% endif %}

        <div class="warning">
            <strong>⚠️ Important:</strong> Use the form below to update the NEW tax slabs for accurate comparison.
        </div>

        <div class="tables-container">
            <div class="table-section old-table">
                <h2>📊 OLD Tax System</h2>
                <p><strong>Current/Previous Tax Slabs</strong></p>
                <table>
                    <thead>
                        <tr>
                            <th>Salary Range (ETB)</th>
                            <th>Tax Rate</th>
                            <th>Deduction (ETB)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for slab in old_slabs %}
                        <tr>
                            <td>
                                {% if slab.max > 999999 %}
                                    {{ "{:,.0f}".format(slab.min) }}+
                                {% else %}
                                    {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }}
                                {% endif %}
                            </td>
                            <td>{{ "{:.0f}".format(slab.rate * 100) }}%</td>
                            <td>{{ "{:,.2f}".format(slab.deduction) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="table-section new-table">
                <h2>🆕 NEW Tax System</h2>
                <p><strong>Updated Tax Slabs</strong></p>
                <table>
                    <thead>
                        <tr>
                            <th>Salary Range (ETB)</th>
                            <th>Tax Rate</th>
                            <th>Deduction (ETB)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for slab in new_slabs %}
                        <tr>
                            <td>
                                {% if slab.max > 999999 %}
                                    {{ "{:,.0f}".format(slab.min) }}+
                                {% else %}
                                    {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }}
                                {% endif %}
                            </td>
                            <td>{{ "{:.0f}".format(slab.rate * 100) }}%</td>
                            <td>{{ "{:,.2f}".format(slab.deduction) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="editable-form">
            <h2>✏️ Edit NEW Tax Slabs</h2>
            <p>Update the tax slabs below and click "Update Tax Slabs" to apply changes:</p>

            <form method="post">
                <div class="form-row">
                    <label>Min Salary (ETB)</label>
                    <label>Max Salary (ETB)</label>
                    <label>Tax Rate (%)</label>
                    <label>Deduction (ETB)</label>
                </div>

                {% for i in range(7) %}
                <div class="form-row">
                    <input type="number" name="min_{{ i }}" value="{{ new_slabs[i].min if new_slabs[i].min < 999999 else '' }}" step="0.01" required>
                    <input type="text" name="max_{{ i }}" value="{% if new_slabs[i].max > 999999 %}∞{% else %}{{ new_slabs[i].max }}{% endif %}" placeholder="Enter max or ∞">
                    <input type="number" name="rate_{{ i }}" value="{{ (new_slabs[i].rate * 100)|round(1) }}" step="0.1" min="0" max="100" required>
                    <input type="number" name="deduction_{{ i }}" value="{{ new_slabs[i].deduction }}" step="0.01" min="0" required>
                </div>
                {% endfor %}

                <button type="submit" class="submit-btn">🔄 Update Tax Slabs</button>
            </form>
        </div>

        <div style="margin-top: 3em; padding: 2em; background-color: #f8f9fa; border-radius: 10px;">
            <h3>📝 How to Update New Tax Slabs:</h3>
            <ol>
                <li>Provide the new tax bracket ranges (e.g., 0-800, 801-2000, etc.)</li>
                <li>Specify the tax rate for each bracket (e.g., 0%, 12%, 15%, etc.)</li>
                <li>Include the deduction amount for each bracket</li>
                <li>Mention any changes to pension rates (currently 7% employee, 11% employer)</li>
            </ol>
            
            <h4>📋 Template for New Tax Slabs:</h4>
            <pre style="background-color: #e9ecef; padding: 1em; border-radius: 5px; overflow-x: auto;">
Slab 1: 0 - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
Slab 2: [START] - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
Slab 3: [START] - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
...
            </pre>
        </div>

        <div style="margin-top: 2em; padding: 1.5em; background-color: #e8f5e8; border-radius: 10px;">
            <h4>💡 Additional Information:</h4>
            <ul>
                <li><strong>Pension Rates:</strong> Employee 7%, Employer 11% (assumed same for both systems)</li>
                <li><strong>Formula:</strong> Income Tax = (Gross Salary × Tax Rate) - Deduction</li>
                <li><strong>Net Income:</strong> Gross Salary - Income Tax - Employee Pension</li>
            </ul>
        </div>
    </div>
</body>
</html>
