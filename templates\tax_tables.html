<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ethiopian Tax Tables Comparison</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 2em; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: auto; 
            padding: 2em; 
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 2em;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 2em;
        }
        .nav-links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 1em;
            font-weight: bold;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .tables-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2em;
            margin-top: 2em;
        }
        .table-section {
            padding: 1.5em;
            border-radius: 10px;
        }
        .old-table {
            background-color: #fff8e1;
            border: 2px solid #ffc107;
        }
        .new-table {
            background-color: #e3f2fd;
            border: 2px solid #2196f3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1em;
        }
        th, td {
            padding: 0.8em;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: rgba(0,0,0,0.1);
            font-weight: bold;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffc107;
            color: #856404;
            padding: 1em;
            border-radius: 5px;
            margin-bottom: 2em;
        }
        .placeholder {
            color: #6c757d;
            font-style: italic;
        }
        .highlight {
            background-color: #ffffcc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇹 Ethiopian Tax Tables Comparison</h1>
            <p>Side-by-Side View of Old vs New Tax Slabs</p>
        </div>

        <div class="nav-links">
            <a href="/">Calculator</a>
            <a href="/tables">Tax Tables</a>
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> The NEW tax table currently shows placeholder data. 
            Please provide the updated tax slabs for accurate comparison.
        </div>

        <div class="tables-container">
            <div class="table-section old-table">
                <h2>📊 OLD Tax System</h2>
                <p><strong>Current/Previous Tax Slabs</strong></p>
                <table>
                    <thead>
                        <tr>
                            <th>Salary Range (ETB)</th>
                            <th>Tax Rate</th>
                            <th>Deduction (ETB)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for slab in old_slabs %}
                        <tr>
                            <td>
                                {% if slab.max > 999999 %}
                                    {{ "{:,.0f}".format(slab.min) }}+
                                {% else %}
                                    {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }}
                                {% endif %}
                            </td>
                            <td>{{ "{:.0f}".format(slab.rate * 100) }}%</td>
                            <td>{{ "{:,.2f}".format(slab.deduction) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="table-section new-table">
                <h2>🆕 NEW Tax System</h2>
                <p><strong>Updated Tax Slabs</strong> <span class="placeholder">(To be updated)</span></p>
                <table>
                    <thead>
                        <tr>
                            <th>Salary Range (ETB)</th>
                            <th>Tax Rate</th>
                            <th>Deduction (ETB)</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for slab in new_slabs %}
                        <tr class="placeholder">
                            <td>
                                {% if slab.max > 999999 %}
                                    {{ "{:,.0f}".format(slab.min) }}+ <small>(placeholder)</small>
                                {% else %}
                                    {{ "{:,.0f}".format(slab.min) }} - {{ "{:,.0f}".format(slab.max) }} <small>(placeholder)</small>
                                {% endif %}
                            </td>
                            <td>{{ "{:.0f}".format(slab.rate * 100) }}% <small>(placeholder)</small></td>
                            <td>{{ "{:,.2f}".format(slab.deduction) }} <small>(placeholder)</small></td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div style="margin-top: 3em; padding: 2em; background-color: #f8f9fa; border-radius: 10px;">
            <h3>📝 How to Update New Tax Slabs:</h3>
            <ol>
                <li>Provide the new tax bracket ranges (e.g., 0-800, 801-2000, etc.)</li>
                <li>Specify the tax rate for each bracket (e.g., 0%, 12%, 15%, etc.)</li>
                <li>Include the deduction amount for each bracket</li>
                <li>Mention any changes to pension rates (currently 7% employee, 11% employer)</li>
            </ol>
            
            <h4>📋 Template for New Tax Slabs:</h4>
            <pre style="background-color: #e9ecef; padding: 1em; border-radius: 5px; overflow-x: auto;">
Slab 1: 0 - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
Slab 2: [START] - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
Slab 3: [START] - [END] ETB, Rate: [X]%, Deduction: [Y] ETB
...
            </pre>
        </div>

        <div style="margin-top: 2em; padding: 1.5em; background-color: #e8f5e8; border-radius: 10px;">
            <h4>💡 Additional Information:</h4>
            <ul>
                <li><strong>Pension Rates:</strong> Employee 7%, Employer 11% (assumed same for both systems)</li>
                <li><strong>Formula:</strong> Income Tax = (Gross Salary × Tax Rate) - Deduction</li>
                <li><strong>Net Income:</strong> Gross Salary - Income Tax - Employee Pension</li>
            </ul>
        </div>
    </div>
</body>
</html>
