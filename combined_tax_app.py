#!/usr/bin/env python3
"""
Combined Ethiopian Tax Calculator - All-in-One Interface
"""

from flask import Flask, render_template, request, session
import json
import os

app = Flask(__name__)
app.secret_key = 'ethiopian_tax_calculator_secret_key_2024'  # For session management

# OLD TAX SLABS (Current system)
OLD_TAX_SLABS = [
    {"min": 0, "max": 600, "rate": 0.00, "deduction": 0},
    {"min": 601, "max": 1650, "rate": 0.10, "deduction": 60},
    {"min": 1651, "max": 3200, "rate": 0.15, "deduction": 142.50},
    {"min": 3201, "max": 5250, "rate": 0.20, "deduction": 302.50},
    {"min": 5251, "max": 7800, "rate": 0.25, "deduction": 565},
    {"min": 7801, "max": 10900, "rate": 0.30, "deduction": 955},
    {"min": 10901, "max": float('inf'), "rate": 0.35, "deduction": 1500}
]

# NEW TAX SLABS (Updated with your formula)
NEW_TAX_SLABS = [
    {"min": 0, "max": 2000, "rate": 0.00, "deduction": 0},
    {"min": 2001, "max": 4000, "rate": 0.15, "deduction": 300},
    {"min": 4001, "max": 7000, "rate": 0.20, "deduction": 500},
    {"min": 7001, "max": 10000, "rate": 0.25, "deduction": 850},
    {"min": 10001, "max": 14000, "rate": 0.30, "deduction": 1350},
    {"min": 14001, "max": float('inf'), "rate": 0.35, "deduction": 2250}
]

def calculate_tax_with_slabs(gross_salary, tax_slabs):
    """Calculate tax using provided tax slabs"""
    for slab in tax_slabs:
        if slab["min"] <= gross_salary <= slab["max"]:
            income_tax = (gross_salary * slab["rate"]) - slab["deduction"]
            break
    else:
        # If no slab found, use the highest slab
        income_tax = (gross_salary * tax_slabs[-1]["rate"]) - tax_slabs[-1]["deduction"]

    # Ensure tax is not negative
    income_tax = max(0, income_tax)

    # Pension calculations (assuming same for both systems)
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income
    }

def calculate_tax_with_new_formula(gross_salary):
    """
    Calculate tax using your exact formula:
    =IF(G8<=2000,0,IF(G8<=4000,(G8-2000)*15%, IF(G8<=7001,300+(G8-4000)*20%,IF(G8<=10001,900+(G8-7000)*25%,IF(G8<=14000,1650+(G8-10000)*30%,IF(G8>14000,2850+(G8-14000)*35%))))))
    """
    if gross_salary <= 2000:
        income_tax = 0
    elif gross_salary <= 4000:
        income_tax = (gross_salary - 2000) * 0.15
    elif gross_salary <= 7000:
        income_tax = 300 + (gross_salary - 4000) * 0.20
    elif gross_salary <= 10000:
        income_tax = 900 + (gross_salary - 7000) * 0.25
    elif gross_salary <= 14000:
        income_tax = 1650 + (gross_salary - 10000) * 0.30
    else:  # gross_salary > 14000
        income_tax = 2850 + (gross_salary - 14000) * 0.35

    # Ensure tax is not negative
    income_tax = max(0, income_tax)

    # Pension calculations (assuming same for both systems)
    employee_pension = gross_salary * 0.07
    employer_pension = gross_salary * 0.11
    net_income = gross_salary - income_tax - employee_pension

    return {
        "gross_salary": gross_salary,
        "income_tax": income_tax,
        "employee_pension": employee_pension,
        "employer_pension": employer_pension,
        "net_income": net_income
    }

def format_currency(amount):
    """Format amount as Ethiopian Birr"""
    return f"{amount:,.2f} ETB"

def save_tax_slabs_to_file():
    """Save current NEW_TAX_SLABS to a JSON file"""
    try:
        print(f"Attempting to save tax slabs to file...")
        with open('saved_tax_slabs.json', 'w') as f:
            json.dump(NEW_TAX_SLABS, f, indent=2)
        print(f"Tax slabs saved successfully to saved_tax_slabs.json")
    except Exception as e:
        print(f"Error saving tax slabs: {e}")

def load_tax_slabs_from_file():
    """Load tax slabs from JSON file if it exists"""
    global NEW_TAX_SLABS
    try:
        if os.path.exists('saved_tax_slabs.json'):
            with open('saved_tax_slabs.json', 'r') as f:
                loaded_slabs = json.load(f)
                # Validate the loaded data
                if len(loaded_slabs) == 7 and all('min' in slab and 'max' in slab and 'rate' in slab and 'deduction' in slab for slab in loaded_slabs):
                    NEW_TAX_SLABS = loaded_slabs
                    print("Tax slabs loaded from saved file")
    except Exception as e:
        print(f"Error loading tax slabs: {e}")

def save_form_data_to_session(form_data):
    """Save form data to session for persistence"""
    session['last_tax_form_data'] = form_data

def get_form_data_from_session():
    """Get last form data from session"""
    return session.get('last_tax_form_data', {})

@app.route('/', methods=['GET', 'POST'])
def index():
    global NEW_TAX_SLABS
    results = None
    message = None
    
    if request.method == 'POST':
        # Check if it's a salary calculation request
        if 'gross_salary' in request.form:
            try:
                gross_salary = float(request.form['gross_salary'])
                
                # Calculate with both old and new systems
                old_result = calculate_tax_with_slabs(gross_salary, OLD_TAX_SLABS)
                new_result = calculate_tax_with_new_formula(gross_salary)
                
                # Calculate differences
                tax_difference = new_result['income_tax'] - old_result['income_tax']
                net_difference = new_result['net_income'] - old_result['net_income']
                
                results = {
                    "gross_salary": format_currency(gross_salary),
                    "old": {
                        "income_tax": format_currency(old_result['income_tax']),
                        "employee_pension": format_currency(old_result['employee_pension']),
                        "employer_pension": format_currency(old_result['employer_pension']),
                        "net_income": format_currency(old_result['net_income'])
                    },
                    "new": {
                        "income_tax": format_currency(new_result['income_tax']),
                        "employee_pension": format_currency(new_result['employee_pension']),
                        "employer_pension": format_currency(new_result['employer_pension']),
                        "net_income": format_currency(new_result['net_income'])
                    },
                    "differences": {
                        "tax_difference": format_currency(tax_difference),
                        "net_difference": format_currency(net_difference),
                        "tax_change_percent": f"{(tax_difference/old_result['income_tax']*100) if old_result['income_tax'] > 0 else 0:.1f}%",
                        "net_change_percent": f"{(net_difference/old_result['net_income']*100) if old_result['net_income'] > 0 else 0:.1f}%"
                    }
                }
                
            except ValueError:
                results = {"error": "Please enter a valid number for the salary."}
        
        # Check if it's a tax slab update request
        elif 'min_0' in request.form:
            try:
                # Save form data to session for persistence
                form_data = {}
                for i in range(7):
                    form_data[f'min_{i}'] = request.form.get(f'min_{i}', '')
                    form_data[f'max_{i}'] = request.form.get(f'max_{i}', '')
                    form_data[f'rate_{i}'] = request.form.get(f'rate_{i}', '')
                    form_data[f'deduction_{i}'] = request.form.get(f'deduction_{i}', '')
                save_form_data_to_session(form_data)

                # Update NEW_TAX_SLABS with form data
                updated_slabs = []
                for i in range(7):  # Assuming 7 tax slabs
                    min_val = float(request.form.get(f'min_{i}', 0))
                    max_val = request.form.get(f'max_{i}', '')
                    rate = float(request.form.get(f'rate_{i}', 0)) / 100  # Convert percentage to decimal
                    deduction = float(request.form.get(f'deduction_{i}', 0))

                    # Handle the last slab (infinity)
                    if max_val == '' or max_val.lower() == 'inf' or max_val == '∞':
                        max_val = float('inf')
                    else:
                        max_val = float(max_val)

                    updated_slabs.append({
                        "min": min_val,
                        "max": max_val,
                        "rate": rate,
                        "deduction": deduction
                    })

                NEW_TAX_SLABS = updated_slabs
                save_tax_slabs_to_file()  # Save to file for persistence
                message = "✅ New tax slabs updated and saved successfully!"

            except ValueError as e:
                message = f"❌ Error updating tax slabs: Please check your input values."
            except Exception as e:
                message = f"❌ Error: {str(e)}"
    
    # Get saved form data for persistence
    saved_form_data = get_form_data_from_session()

    return render_template('combined_interface.html',
                         old_slabs=OLD_TAX_SLABS,
                         new_slabs=NEW_TAX_SLABS,
                         results=results,
                         message=message,
                         saved_form_data=saved_form_data)

if __name__ == '__main__':
    # Load saved tax slabs on startup
    load_tax_slabs_from_file()
    app.run(debug=True, port=5002)
